import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface PopoverClasses {
  /** Styles applied to the root element. */
  root: string;
  /** Styles applied to the Paper component. */
  paper: string;
  /** Styles applied to the arrow element. */
  arrow: string;
}

export type PopoverClassKey = keyof PopoverClasses;

export function getPopoverUtilityClass(slot: string): string {
  return generateUtilityClass('NovaPopover', slot, 'Nova');
}

const popoverClasses: PopoverClasses = generateUtilityClasses('NovaPopover', ['root', 'paper', 'arrow'], 'Nova');

export default popoverClasses;
