import { styled } from '@pigment-css/react';
import { Modal } from '../Modal';
import { Paper } from '../Paper';

export const PopoverRoot = styled(Modal, {
  name: 'MuiPopover',
  slot: 'Root',
})({});

export const PopoverPaper = styled(Paper, {
  name: 'MuiPopover',
  slot: 'Paper',
})({
  position: 'absolute',
  overflowY: 'auto',
  overflowX: 'hidden',
  // So we see the popover when it's empty.
  // It's most likely on issue on userland.
  minWidth: 16,
  minHeight: 16,
  maxWidth: 'calc(100% - 32px)',
  maxHeight: 'calc(100% - 32px)',
  // We disable the focus ring for mouse, touch and keyboard users.
  outline: 0,
});

export const PopoverArrow = styled('span', {
  name: 'NovaPopover',
  slot: 'Arrow',
})(({ theme }) => ({
  overflow: 'hidden',
  position: 'absolute',
  width: '1em',
  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,
  boxSizing: 'border-box',
  color: theme.vars.palette.surface,
  zIndex: 1,
  // Default position (bottom arrow)
  top: '-0.71em',
  left: '50%',
  transform: 'translateX(-50%)',
  '&::before': {
    content: '""',
    margin: 'auto',
    display: 'block',
    width: '100%',
    height: '100%',
    backgroundColor: 'currentColor',
    transform: 'rotate(45deg)',
  },
}));
